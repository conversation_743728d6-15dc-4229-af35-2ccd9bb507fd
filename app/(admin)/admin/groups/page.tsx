"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Search,
  Filter,
  MoreHorizontal,
  Plus,
  Users,
  MapPin,
  Car,
  Shield,
  AlertTriangle,
  CheckCircle,
  FileText,
  DollarSign,
  Clock,
  Eye,
  Edit,
  MessageSquare,
  BarChart2,
  Activity,
  Download,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
} from "recharts";

// Mock group data
export const groupsData = [
  {
    id: 1,
    name: "Family SUV",
    image: "/placeholder.svg?height=60&width=60",
    members: 5,
    vehicles: 1,
    location: "Cape Town",
    status: "Active",
    compliance: "Compliant",
    created: "15/01/2025",
    lastActivity: "2 hours ago",
    formationStatus: "Completed",
    verificationStatus: "Verified",
    legalEntityStatus: "Registered",
    financialSetupStatus: "Completed",
    partnerIntegrationStatus: "Completed",
    applicationProgress: 100,
    communicationStatus: "Up to date",
  },
  {
    id: 2,
    name: "Weekend Getaway",
    image: "/placeholder.svg?height=60&width=60",
    members: 3,
    vehicles: 1,
    location: "Johannesburg",
    status: "Active",
    compliance: "Compliant",
    created: "10/01/2025",
    lastActivity: "1 day ago",
    formationStatus: "Completed",
    verificationStatus: "Verified",
    legalEntityStatus: "Registered",
    financialSetupStatus: "Completed",
    partnerIntegrationStatus: "In Progress",
    applicationProgress: 85,
    communicationStatus: "Pending Update",
  },
  {
    id: 3,
    name: "Work Commute",
    image: "/placeholder.svg?height=60&width=60",
    members: 4,
    vehicles: 1,
    location: "Durban",
    status: "Active",
    compliance: "Compliant",
    created: "05/01/2025",
    lastActivity: "3 hours ago",
    formationStatus: "Completed",
    verificationStatus: "Verified",
    legalEntityStatus: "Registered",
    financialSetupStatus: "In Progress",
    partnerIntegrationStatus: "Pending",
    applicationProgress: 70,
    communicationStatus: "Up to date",
  },
];

// Mock data for groups needing company formation
export const companyFormationData = [
  {
    id: 4,
    name: "City Commuters",
    members: 8,
    vehicles: 2,
    location: "Cape Town",
    monthlyRevenue: 45000,
    formationStatus: "Ready for Formation",
    businessType: "Ride-sharing Collective",
    estimatedValue: 850000,
    leadMember: "Sarah Johnson",
    leadEmail: "<EMAIL>",
    leadPhone: "+27 82 345 6789",
    dateEligible: "2024-01-20",
    complianceScore: 95,
    financialHealth: "Excellent",
    requiredDocuments: [
      { name: "Memorandum of Incorporation", status: "pending" },
      { name: "Tax Registration", status: "pending" },
      { name: "Banking Resolution", status: "pending" },
      { name: "Shareholder Agreement", status: "pending" },
    ],
    estimatedFormationTime: "6-8 weeks",
    formationCost: 15000,
  },
  {
    id: 5,
    name: "Weekend Warriors",
    members: 6,
    vehicles: 3,
    location: "Johannesburg",
    monthlyRevenue: 62000,
    formationStatus: "Documentation in Progress",
    businessType: "Vehicle Rental Cooperative",
    estimatedValue: 1200000,
    leadMember: "Michael Chen",
    leadEmail: "<EMAIL>",
    leadPhone: "+27 83 456 7890",
    dateEligible: "2024-01-18",
    complianceScore: 88,
    financialHealth: "Good",
    requiredDocuments: [
      { name: "Memorandum of Incorporation", status: "completed" },
      { name: "Tax Registration", status: "in_progress" },
      { name: "Banking Resolution", status: "pending" },
      { name: "Shareholder Agreement", status: "completed" },
    ],
    estimatedFormationTime: "4-6 weeks",
    formationCost: 18000,
  },
  {
    id: 6,
    name: "Family Fleet",
    members: 12,
    vehicles: 4,
    location: "Durban",
    monthlyRevenue: 78000,
    formationStatus: "Legal Review",
    businessType: "Family Vehicle Trust",
    estimatedValue: 1800000,
    leadMember: "David Williams",
    leadEmail: "<EMAIL>",
    leadPhone: "+27 84 567 8901",
    dateEligible: "2024-01-15",
    complianceScore: 92,
    financialHealth: "Excellent",
    requiredDocuments: [
      { name: "Memorandum of Incorporation", status: "completed" },
      { name: "Tax Registration", status: "completed" },
      { name: "Banking Resolution", status: "in_progress" },
      { name: "Shareholder Agreement", status: "completed" },
    ],
    estimatedFormationTime: "2-3 weeks",
    formationCost: 22000,
  },
  {
    id: 7,
    name: "Business Mobility",
    members: 15,
    vehicles: 5,
    location: "Pretoria",
    monthlyRevenue: 95000,
    formationStatus: "Ready for Formation",
    businessType: "Corporate Fleet Management",
    estimatedValue: 2200000,
    leadMember: "Lisa Thompson",
    leadEmail: "<EMAIL>",
    leadPhone: "+27 85 678 9012",
    dateEligible: "2024-01-12",
    complianceScore: 97,
    financialHealth: "Excellent",
    requiredDocuments: [
      { name: "Memorandum of Incorporation", status: "pending" },
      { name: "Tax Registration", status: "pending" },
      { name: "Banking Resolution", status: "pending" },
      { name: "Shareholder Agreement", status: "pending" },
    ],
    estimatedFormationTime: "8-10 weeks",
    formationCost: 28000,
  },
  {
    id: 8,
    name: "Urban Mobility",
    members: 10,
    vehicles: 3,
    location: "Cape Town",
    monthlyRevenue: 55000,
    formationStatus: "Registered",
    businessType: "Vehicle Sharing Cooperative",
    estimatedValue: 1400000,
    leadMember: "James Wilson",
    leadEmail: "<EMAIL>",
    leadPhone: "+27 86 789 0123",
    dateEligible: "2024-01-08",
    complianceScore: 100,
    financialHealth: "Excellent",
    requiredDocuments: [
      { name: "Memorandum of Incorporation", status: "completed" },
      { name: "Tax Registration", status: "completed" },
      { name: "Banking Resolution", status: "completed" },
      { name: "Shareholder Agreement", status: "completed" },
    ],
    estimatedFormationTime: "Completed",
    formationCost: 25000,
  },
];

export default function GroupOversight() {
  const router = useRouter();
  const [currentTab, setCurrentTab] = useState("dashboard");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGroups, setSelectedGroups] = useState<number[]>([]);
  const [statusFilter, setStatusFilter] = useState("All");
  const [complianceFilter, setComplianceFilter] = useState("All");
  const [formationFilter, setFormationFilter] = useState("All");

  // Company formation specific filters
  const [formationSearchQuery, setFormationSearchQuery] = useState("");
  const [formationStatusFilter, setFormationStatusFilter] = useState("All");

  // Handle select all groups
  const handleSelectAll = () => {
    if (selectedGroups.length === groupsData.length) {
      setSelectedGroups([]);
    } else {
      setSelectedGroups(groupsData.map((group) => group.id));
    }
  };

  // Handle select individual group
  const handleSelectGroup = (groupId: number) => {
    if (selectedGroups.includes(groupId)) {
      setSelectedGroups(selectedGroups.filter((id) => id !== groupId));
    } else {
      setSelectedGroups([...selectedGroups, groupId]);
    }
  };

  // Handle view group details
  const handleViewGroup = (group: any) => {
    router.push(`/admin/groups/${group.id}`);
  };

  // Filter groups based on search query and filters
  const filteredGroups = groupsData.filter((group) => {
    const matchesSearch = group.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesStatus =
      statusFilter === "All" || group.status === statusFilter;
    const matchesCompliance =
      complianceFilter === "All" || group.compliance === complianceFilter;
    const matchesFormation =
      formationFilter === "All" || group.formationStatus === formationFilter;

    return (
      matchesSearch && matchesStatus && matchesCompliance && matchesFormation
    );
  });

  // Get compliance badge color
  const getComplianceBadge = (compliance: string) => {
    switch (compliance) {
      case "Compliant":
        return {
          bg: "bg-[#00963920]",
          text: "text-[#009639]",
          icon: <CheckCircle size={14} className="mr-1" />,
        };
      case "Warning":
        return {
          bg: "bg-yellow-100",
          text: "text-yellow-800",
          icon: <AlertTriangle size={14} className="mr-1" />,
        };
      case "Non-compliant":
        return {
          bg: "bg-red-100",
          text: "text-red-800",
          icon: <AlertTriangle size={14} className="mr-1" />,
        };
      default:
        return { bg: "bg-gray-100", text: "text-gray-800", icon: null };
    }
  };

  // Statistics data
  const statsData = {
    totalGroups: groupsData.length,
    activeGroups: groupsData.filter((group) => group.status === "Active")
      .length,
    complianceRate: Math.round(
      (groupsData.filter((group) => group.compliance === "Compliant").length /
        groupsData.length) *
        100
    ),
    avgUtilization: 78,
  };

  // Group types data for chart
  const groupTypesData = [
    { name: "Family", value: 45, color: "#009639" },
    { name: "Business", value: 30, color: "#007A2F" },
    { name: "Community", value: 25, color: "#FFD700" },
  ];

  // Group activity data for chart
  const groupActivityData = [
    { name: "Mon", value: 65 },
    { name: "Tue", value: 75 },
    { name: "Wed", value: 85 },
    { name: "Thu", value: 70 },
    { name: "Fri", value: 90 },
    { name: "Sat", value: 95 },
    { name: "Sun", value: 80 },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Group Management</h1>
        <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
          <Plus size={16} />
          Create Group
        </Button>
      </div>

      {/* Tabs */}
      <Tabs
        value={currentTab}
        onValueChange={setCurrentTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart2 size={16} />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="formation" className="flex items-center gap-2">
            <FileText size={16} />
            Company Formation ({companyFormationData.length})
          </TabsTrigger>
        </TabsList>

        {/* Dashboard Tab */}
        <TabsContent value="dashboard" className="space-y-6">
          {/* Statistics Section */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-gray-500">Total Groups</p>
                    <p className="text-2xl font-bold mt-1">
                      {statsData.totalGroups}
                    </p>
                  </div>
                  <div className="bg-[#e6ffe6] p-2 rounded-lg">
                    <Users size={20} className="text-[#009639]" />
                  </div>
                </div>
                <div className="flex items-center mt-2">
                  <span className="text-green-500 text-xs font-medium">
                    +12%
                  </span>
                  <span className="text-xs text-gray-500 ml-2">
                    vs last month
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-gray-500">Active Groups</p>
                    <p className="text-2xl font-bold mt-1">
                      {statsData.activeGroups}
                    </p>
                  </div>
                  <div className="bg-[#e6ffe6] p-2 rounded-lg">
                    <CheckCircle size={20} className="text-[#009639]" />
                  </div>
                </div>
                <div className="flex items-center mt-2">
                  <span className="text-green-500 text-xs font-medium">
                    +8%
                  </span>
                  <span className="text-xs text-gray-500 ml-2">
                    vs last month
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-gray-500">Compliance Rate</p>
                    <p className="text-2xl font-bold mt-1">
                      {statsData.complianceRate}%
                    </p>
                  </div>
                  <div className="bg-[#e6ffe6] p-2 rounded-lg">
                    <Shield size={20} className="text-[#009639]" />
                  </div>
                </div>
                <div className="flex items-center mt-2">
                  <span className="text-green-500 text-xs font-medium">
                    +5%
                  </span>
                  <span className="text-xs text-gray-500 ml-2">
                    vs last month
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-gray-500">Average Utilization</p>
                    <p className="text-2xl font-bold mt-1">
                      {statsData.avgUtilization}%
                    </p>
                  </div>
                  <div className="bg-[#e6ffe6] p-2 rounded-lg">
                    <Car size={20} className="text-[#009639]" />
                  </div>
                </div>
                <div className="flex items-center mt-2">
                  <span className="text-green-500 text-xs font-medium">
                    +3%
                  </span>
                  <span className="text-xs text-gray-500 ml-2">
                    vs last month
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Group Distribution */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center mb-4">
                  <MapPin size={20} className="text-[#009639] mr-2" />
                  <h3 className="text-lg font-semibold">Groups by Location</h3>
                </div>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">Cape Town</span>
                      <span className="text-sm font-medium">42%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-[#009639] h-2 rounded-full"
                        style={{ width: "42%" }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">Johannesburg</span>
                      <span className="text-sm font-medium">28%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-[#007A2F] h-2 rounded-full"
                        style={{ width: "28%" }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">Durban</span>
                      <span className="text-sm font-medium">18%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-[#20c997] h-2 rounded-full"
                        style={{ width: "18%" }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">Other</span>
                      <span className="text-sm font-medium">12%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gray-400 h-2 rounded-full"
                        style={{ width: "12%" }}
                      ></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center mb-4">
                  <DollarSign size={20} className="text-[#009639] mr-2" />
                  <h3 className="text-lg font-semibold">Financial Health</h3>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">
                        Average Monthly Income
                      </p>
                      <p className="text-xl font-bold mt-1">R 8,450</p>
                    </div>
                    <div className="flex items-center">
                      <span className="text-green-500 text-xs font-medium">
                        +7.2%
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Cost Recovery Rate</p>
                      <p className="text-xl font-bold mt-1">92%</p>
                    </div>
                    <div className="flex items-center">
                      <span className="text-green-500 text-xs font-medium">
                        +2.5%
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Payment Compliance</p>
                      <p className="text-xl font-bold mt-1">86%</p>
                    </div>
                    <div className="flex items-center">
                      <span className="text-yellow-500 text-xs font-medium">
                        -1.3%
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Users size={18} className="text-[#009639] mr-2" />
                  Group Types
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={groupTypesData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) =>
                          `${name} ${(percent * 100).toFixed(0)}%`
                        }
                      >
                        {groupTypesData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Activity size={18} className="text-[#009639] mr-2" />
                  Group Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={groupActivityData}>
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar
                        dataKey="value"
                        fill="#009639"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters Section */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="relative flex-1">
                  <Search
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                    size={18}
                  />
                  <Input
                    type="text"
                    placeholder="Search groups..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex flex-col md:flex-row gap-4">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <div className="flex items-center">
                        <Filter size={16} className="mr-2 text-gray-400" />
                        <SelectValue placeholder="Status" />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="All">All Statuses</SelectItem>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select
                    value={complianceFilter}
                    onValueChange={setComplianceFilter}
                  >
                    <SelectTrigger className="w-[180px]">
                      <div className="flex items-center">
                        <Shield size={16} className="mr-2 text-gray-400" />
                        <SelectValue placeholder="Compliance" />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="All">All Compliance</SelectItem>
                      <SelectItem value="Compliant">Compliant</SelectItem>
                      <SelectItem value="Warning">Warning</SelectItem>
                      <SelectItem value="Non-compliant">
                        Non-compliant
                      </SelectItem>
                    </SelectContent>
                  </Select>

                  <Select
                    value={formationFilter}
                    onValueChange={setFormationFilter}
                  >
                    <SelectTrigger className="w-[180px]">
                      <div className="flex items-center">
                        <FileText size={16} className="mr-2 text-gray-400" />
                        <SelectValue placeholder="Formation" />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="All">All Formations</SelectItem>
                      <SelectItem value="Completed">Completed</SelectItem>
                      <SelectItem value="In Progress">In Progress</SelectItem>
                      <SelectItem value="Pending">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Groups Table */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg flex items-center">
                  <Users size={18} className="text-[#009639] mr-2" />
                  Groups
                </CardTitle>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="text-gray-600">
                    <Download size={16} className="mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">
                      <input
                        type="checkbox"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                        checked={selectedGroups.length === groupsData.length}
                        onChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>Group Name</TableHead>
                    <TableHead>Members</TableHead>
                    <TableHead>Vehicles</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Compliance</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredGroups.map((group) => {
                    const complianceBadge = getComplianceBadge(
                      group.compliance
                    );

                    return (
                      <TableRow
                        key={group.id}
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => handleViewGroup(group)}
                      >
                        <TableCell onClick={(e) => e.stopPropagation()}>
                          <input
                            type="checkbox"
                            className="rounded text-[#009639] focus:ring-[#009639]"
                            checked={selectedGroups.includes(group.id)}
                            onChange={() => handleSelectGroup(group.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <div className="w-10 h-10 rounded-lg bg-[#e6ffe6] flex items-center justify-center mr-3">
                              <Users size={20} className="text-[#009639]" />
                            </div>
                            <span className="font-medium">{group.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Users size={16} className="mr-2 text-gray-400" />
                            {group.members}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Car size={16} className="mr-2 text-gray-400" />
                            {group.vehicles}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <MapPin size={16} className="mr-2 text-gray-400" />
                            {group.location}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={
                              group.status === "Active"
                                ? "bg-[#00963920] text-[#009639]"
                                : "bg-gray-100 text-gray-800"
                            }
                          >
                            {group.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={`${complianceBadge.bg} ${complianceBadge.text}`}
                          >
                            <span className="flex items-center">
                              {complianceBadge.icon}
                              {group.compliance}
                            </span>
                          </Badge>
                        </TableCell>
                        <TableCell
                          className="text-right"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-gray-500 hover:text-gray-700"
                              onClick={() =>
                                router.push(`/admin/groups/${group.id}`)
                              }
                            >
                              <Eye size={16} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-gray-500 hover:text-gray-700"
                            >
                              <Edit size={16} />
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-gray-500 hover:text-gray-700"
                                >
                                  <MoreHorizontal size={16} />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <MessageSquare size={16} className="mr-2" />
                                  Message
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Shield size={16} className="mr-2" />
                                  Compliance Check
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  <AlertTriangle size={16} className="mr-2" />
                                  Suspend Group
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Company Formation Tab */}
        <TabsContent value="formation" className="space-y-6">
          {/* Formation Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-gray-500">Ready for Formation</p>
                    <p className="text-2xl font-bold mt-1">
                      {
                        companyFormationData.filter(
                          (g) => g.formationStatus === "Ready for Formation"
                        ).length
                      }
                    </p>
                  </div>
                  <div className="bg-[#e6ffe6] p-2 rounded-lg">
                    <FileText size={20} className="text-[#009639]" />
                  </div>
                </div>
                <div className="flex items-center mt-2">
                  <span className="text-green-500 text-xs font-medium">
                    High Priority
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-gray-500">In Progress</p>
                    <p className="text-2xl font-bold mt-1">
                      {
                        companyFormationData.filter(
                          (g) =>
                            g.formationStatus === "Documentation in Progress" ||
                            g.formationStatus === "Legal Review"
                        ).length
                      }
                    </p>
                  </div>
                  <div className="bg-blue-100 p-2 rounded-lg">
                    <Clock size={20} className="text-blue-600" />
                  </div>
                </div>
                <div className="flex items-center mt-2">
                  <span className="text-blue-500 text-xs font-medium">
                    Active
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-gray-500">Total Value</p>
                    <p className="text-2xl font-bold mt-1">
                      R
                      {(
                        companyFormationData.reduce(
                          (sum, g) => sum + g.estimatedValue,
                          0
                        ) / 1000000
                      ).toFixed(1)}
                      M
                    </p>
                  </div>
                  <div className="bg-yellow-100 p-2 rounded-lg">
                    <DollarSign size={20} className="text-yellow-600" />
                  </div>
                </div>
                <div className="flex items-center mt-2">
                  <span className="text-yellow-600 text-xs font-medium">
                    Combined
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-gray-500">Registered</p>
                    <p className="text-2xl font-bold mt-1">
                      {
                        companyFormationData.filter(
                          (group) => group.formationStatus === "Registered"
                        ).length
                      }
                    </p>
                  </div>
                  <div className="bg-purple-100 p-2 rounded-lg">
                    <CheckCircle size={20} className="text-purple-600" />
                  </div>
                </div>
                <div className="flex items-center mt-2">
                  <span className="text-purple-600 text-xs font-medium">
                    Completed
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Formation Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="relative flex-1">
                  <Search
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                    size={18}
                  />
                  <Input
                    type="text"
                    placeholder="Search groups for formation..."
                    value={formationSearchQuery}
                    onChange={(e) => setFormationSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex flex-col md:flex-row gap-4">
                  <Select
                    value={formationStatusFilter}
                    onValueChange={setFormationStatusFilter}
                  >
                    <SelectTrigger className="w-[200px]">
                      <div className="flex items-center">
                        <Filter size={16} className="mr-2 text-gray-400" />
                        <SelectValue placeholder="Formation Status" />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="All">All Statuses</SelectItem>
                      <SelectItem value="Ready for Formation">
                        Ready for Formation
                      </SelectItem>
                      <SelectItem value="Documentation in Progress">
                        Documentation in Progress
                      </SelectItem>
                      <SelectItem value="Legal Review">Legal Review</SelectItem>
                      <SelectItem value="Registered">Registered</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Formation Table */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg flex items-center">
                  <FileText size={18} className="text-[#009639] mr-2" />
                  Co-ownership Groups Requiring Company Formation
                </CardTitle>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="text-gray-600">
                    <Download size={16} className="mr-2" />
                    Export Formation Report
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Group Details</TableHead>
                    <TableHead>Formation Status</TableHead>
                    <TableHead>Lead Contact</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {companyFormationData
                    .filter((group) => {
                      const matchesSearch = group.name
                        .toLowerCase()
                        .includes(formationSearchQuery.toLowerCase());
                      const matchesStatus =
                        formationStatusFilter === "All" ||
                        group.formationStatus === formationStatusFilter;
                      return matchesSearch && matchesStatus;
                    })
                    .map((group) => (
                      <TableRow key={group.id} className="hover:bg-gray-50">
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">{group.name}</div>
                            <div className="text-sm text-gray-500">
                              {group.members} members • {group.vehicles}{" "}
                              vehicles
                            </div>
                            <div className="text-sm text-gray-500">
                              <MapPin size={12} className="inline mr-1" />
                              {group.location}
                            </div>
                            <div className="text-sm text-gray-500">
                              Created:{" "}
                              {new Date(
                                group.dateEligible
                              ).toLocaleDateString()}
                            </div>
                          </div>
                        </TableCell>

                        <TableCell>
                          <Badge
                            variant="outline"
                            className={
                              group.formationStatus === "Ready for Formation"
                                ? "bg-green-100 text-green-800"
                                : group.formationStatus === "Legal Review"
                                  ? "bg-blue-100 text-blue-800"
                                  : group.formationStatus ===
                                      "Documentation in Progress"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : group.formationStatus === "Registered"
                                      ? "bg-purple-100 text-purple-800"
                                      : "bg-gray-100 text-gray-800"
                            }
                          >
                            {group.formationStatus}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium text-sm">
                              {group.leadMember}
                            </div>
                            <div className="text-sm text-gray-500">
                              {group.leadEmail}
                            </div>
                            <div className="text-sm text-gray-500">
                              {group.leadPhone}
                            </div>
                          </div>
                        </TableCell>

                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            {group.formationStatus === "Ready for Formation" ? (
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-[#009639] border-[#009639] hover:bg-[#009639] hover:text-white"
                              >
                                <FileText size={14} className="mr-1" />
                                Start Formation
                              </Button>
                            ) : group.formationStatus === "Registered" ? (
                              <Badge
                                variant="outline"
                                className="bg-purple-100 text-purple-800"
                              >
                                <CheckCircle size={14} className="mr-1" />
                                Completed
                              </Badge>
                            ) : (
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-blue-600 border-blue-600 hover:bg-blue-600 hover:text-white"
                              >
                                <FileText size={14} className="mr-1" />
                                Continue Formation
                              </Button>
                            )}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-gray-500 hover:text-gray-700"
                                >
                                  <MoreHorizontal size={16} />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>
                                  Formation Actions
                                </DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye size={16} className="mr-2" />
                                  View Group Details
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Users size={16} className="mr-2" />
                                  View All Members
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <MessageSquare size={16} className="mr-2" />
                                  Request Member Details
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <FileText size={16} className="mr-2" />
                                  Setup SPV Company
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Download size={16} className="mr-2" />
                                  Upload Incorporation Docs
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <CheckCircle size={16} className="mr-2" />
                                  Mark as Registered
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

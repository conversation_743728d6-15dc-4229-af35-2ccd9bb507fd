"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Filter,
  ChevronDown,
  MoreVertical,
  Car,
  Eye,
  DollarSign,
  Percent,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  Users,
  Download,
  Mail,
  TrendingUp,
  AlertTriangle,
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface VehicleListing {
  id: string;
  ownerName: string;
  ownerEmail: string;
  vehicleName: string;
  make: string;
  model: string;
  year: number;
  submittedDate: string;
  status: "pending" | "under_review" | "approved" | "rejected";
  decisionDate?: string;
  decisionReason?: string;
  listingType: "rental" | "co_ownership" | "lease_to_own";
  askingPrice: number;
  fractionOffer?: number;
  condition: "new" | "used";
  mileage?: number;
  documents: {
    name: string;
    uploaded: boolean;
    verified?: boolean;
  }[];
  images: string[];
  location: string;
}

export default function VehicleListingsPage() {
  const [currentTab, setCurrentTab] = useState<"active" | "history">("active");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "pending" | "under_review" | "approved" | "rejected"
  >("all");
  const [filterType, setFilterType] = useState<
    "all" | "rental" | "co_ownership" | "lease_to_own"
  >("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Mock data - replace with actual data fetching
  const [listings] = useState<VehicleListing[]>([
    {
      id: "1",
      ownerName: "Lisa Johnson",
      ownerEmail: "<EMAIL>",
      vehicleName: "BMW X3 2021",
      make: "BMW",
      model: "X3",
      year: 2021,
      submittedDate: "2024-01-15",
      status: "pending",
      listingType: "co_ownership",
      askingPrice: 450000,
      fractionOffer: 0.25,
      condition: "used",
      mileage: 45000,
      location: "Cape Town",
      documents: [
        { name: "Vehicle Registration", uploaded: true, verified: false },
        { name: "Insurance Certificate", uploaded: true, verified: true },
        { name: "Vehicle Photos", uploaded: true, verified: true },
        { name: "Service History", uploaded: true, verified: false },
      ],
      images: ["/images/cars/bmw-x3.jpg"],
    },
    {
      id: "2",
      ownerName: "Michael Chen",
      ownerEmail: "<EMAIL>",
      vehicleName: "Toyota Corolla 2022",
      make: "Toyota",
      model: "Corolla",
      year: 2022,
      submittedDate: "2024-01-14",
      status: "under_review",
      listingType: "rental",
      askingPrice: 2800,
      condition: "used",
      mileage: 25000,
      location: "Johannesburg",
      documents: [
        { name: "Vehicle Registration", uploaded: true, verified: true },
        { name: "Insurance Certificate", uploaded: true, verified: true },
        { name: "Vehicle Photos", uploaded: true, verified: true },
        { name: "Driver's License", uploaded: true, verified: true },
      ],
      images: ["/images/cars/toyota-corolla.jpg"],
    },
    {
      id: "3",
      ownerName: "Sarah Williams",
      ownerEmail: "<EMAIL>",
      vehicleName: "Honda Civic 2023",
      make: "Honda",
      model: "Civic",
      year: 2023,
      submittedDate: "2024-01-10",
      status: "approved",
      decisionDate: "2024-01-12",
      listingType: "rental",
      askingPrice: 3200,
      condition: "new",
      mileage: 5000,
      location: "Durban",
      documents: [
        { name: "Vehicle Registration", uploaded: true, verified: true },
        { name: "Insurance Certificate", uploaded: true, verified: true },
        { name: "Vehicle Photos", uploaded: true, verified: true },
        { name: "Service History", uploaded: true, verified: true },
      ],
      images: ["/images/cars/honda-civic.jpg"],
    },
  ]);

  // Helper functions
  const getStatusColor = (status: VehicleListing["status"]) => {
    switch (status) {
      case "pending":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "under_review":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "approved":
        return "text-green-600 bg-green-50 border-green-200";
      case "rejected":
        return "text-red-600 bg-red-50 border-red-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getStatusIcon = (status: VehicleListing["status"]) => {
    switch (status) {
      case "pending":
        return <Clock size={12} />;
      case "under_review":
        return <Eye size={12} />;
      case "approved":
        return <CheckCircle size={12} />;
      case "rejected":
        return <XCircle size={12} />;
      default:
        return <Clock size={12} />;
    }
  };

  const getTabCount = (tab: "active" | "history") => {
    if (tab === "active") {
      return listings.filter(
        (listing) =>
          listing.status === "pending" || listing.status === "under_review"
      ).length;
    }
    return listings.filter(
      (listing) =>
        listing.status === "approved" || listing.status === "rejected"
    ).length;
  };

  const getFilterCount = (status: typeof filterStatus) => {
    if (status === "all") return listings.length;
    return listings.filter((listing) => listing.status === status).length;
  };

  // Filter listings based on current tab
  const activeListings = listings.filter(
    (listing) =>
      listing.status === "pending" || listing.status === "under_review"
  );

  const historicalListings = listings.filter(
    (listing) => listing.status === "approved" || listing.status === "rejected"
  );

  const currentListings =
    currentTab === "active" ? activeListings : historicalListings;

  const filteredListings = currentListings.filter((listing) => {
    const matchesStatusFilter =
      filterStatus === "all" || listing.status === filterStatus;
    const matchesTypeFilter =
      filterType === "all" || listing.listingType === filterType;
    const matchesSearch =
      listing.ownerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      listing.vehicleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      listing.location.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesStatusFilter && matchesTypeFilter && matchesSearch;
  });

  const getListingTypeLabel = (type: string) => {
    switch (type) {
      case "rental":
        return "Rental";
      case "co_ownership":
        return "Co-ownership";
      case "lease_to_own":
        return "Lease-to-Own";
      default:
        return type;
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Vehicle Listings</h1>
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search listings..."
              className="pl-10 w-[300px]"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Listing
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Active Listings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Car className="h-5 w-5 text-[#009639]" />
              <span className="text-2xl font-bold">18</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Available Fractions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Percent className="h-5 w-5 text-blue-600" />
              <span className="text-2xl font-bold text-blue-600">42</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Total Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-[#009639]" />
              <span className="text-2xl font-bold">R 4.2M</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Total Views</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-gray-600" />
              <span className="text-2xl font-bold">1,245</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Listings</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Vehicle</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Fraction Price</TableHead>
                <TableHead>Available</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Views</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {listings.map((listing) => (
                <TableRow key={listing.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Car className="h-4 w-4 text-[#009639]" />
                      <Link
                        href={`/admin/vehicles/${listing.id}`}
                        className="text-[#009639] hover:text-[#007A2F]"
                      >
                        {listing.vehicle}
                      </Link>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/groups/${listing.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {listing.group}
                    </Link>
                  </TableCell>
                  <TableCell>R {listing.price.toLocaleString()}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <span>R {listing.fractionPrice.toLocaleString()}</span>
                      <span className="text-xs text-gray-500">
                        ({listing.fractionSize})
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {listing.availableFractions}/{listing.totalFractions}{" "}
                    fractions
                  </TableCell>
                  <TableCell>{listing.location}</TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        listing.status === "Active"
                          ? "bg-green-100 text-green-800"
                          : listing.status === "Sold Out"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {listing.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Eye className="h-3 w-3 text-gray-500" />
                      {listing.views}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/listings/${listing.id}`}
                      className="text-[#009639] hover:text-[#007A2F] font-medium"
                    >
                      View Details
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

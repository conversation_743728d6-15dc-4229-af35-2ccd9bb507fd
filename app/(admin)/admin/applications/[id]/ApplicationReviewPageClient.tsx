"use client";

import React, { useState } from "react";
import {
  ArrowLeft,
  User,
  FileText,
  CheckCircle,
  XCircle,
  Download,
  Eye,
  Clock,
  Star,
  Car,
  Phone,
  Mail,
  AlertTriangle,
} from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface ApplicationData {
  id: string;
  applicantName: string;
  applicantEmail: string;
  applicantPhone: string;
  applicationDate: string;
  status: "pending" | "under_review" | "approved" | "rejected";
  applicationType:
    | "vehicle_lease"
    | "rental_application"
    | "co_ownership_application"
    | "vehicle_listing";
  vehicleId?: string;
  vehicleName?: string;
  listingType?: "rental" | "fractional" | "lease-to-own";
  // E-hailing specific fields
  experience?: {
    hasExperience: boolean;
    company?: string;
    duration?: string;
    workType?: string;
    profileNumber?: string;
  };
  weeklyRate?: number;
  initiationFee?: number;
  paymentArrangement?: boolean;
  // EARN side application fields
  applicantAge?: number;
  applicantGender?: "male" | "female" | "other";
  drivingExperienceYears?: number;
  documentsVerified?: boolean;
  matchesPreferences?: {
    age: boolean;
    gender: boolean;
    experience: boolean;
  };
  // Vehicle listing specific fields
  make?: string;
  model?: string;
  year?: number;
  condition?: "new" | "used";
  mileage?: number;
  askingPrice?: number;
  fractionOffer?: number;
  location?: string;
  description?: string;
  documents: {
    name: string;
    uploaded: boolean;
    verified?: boolean | "failed";
    fileUrl?: string;
  }[];
}

export default function ApplicationReviewPageClient({
  applicationId,
}: {
  applicationId: string;
}) {
  const [currentTab, setCurrentTab] = useState("overview");
  const [decisionReason, setDecisionReason] = useState("");
  const [isDecisionDialogOpen, setIsDecisionDialogOpen] = useState(false);
  const [pendingDecision, setPendingDecision] = useState<
    "approved" | "rejected" | null
  >(null);
  const [documentStatuses, setDocumentStatuses] = useState<
    Record<string, boolean | "failed">
  >({});

  // Helper function to format dates consistently
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatShortDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  // Mock data - replace with actual data fetching based on applicationId
  const getApplicationData = (id: string): ApplicationData => {
    // Simulate different application types based on ID
    if (id === "1" || id.includes("lease")) {
      return {
        id: applicationId,
        applicantName: "John Doe",
        applicantEmail: "<EMAIL>",
        applicantPhone: "+27 82 123 4567",
        applicationDate: "2024-01-15",
        status: "pending",
        applicationType: "vehicle_lease",
        vehicleId: "v1",
        vehicleName: "Suzuki Dzire 2023",
        experience: {
          hasExperience: true,
          company: "Uber",
          duration: "2-5-years",
          workType: "full-time",
          profileNumber: "78432",
        },
        documents: [
          {
            name: "ID Document",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/id.pdf",
          },
          {
            name: "Driver's License",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/license.pdf",
          },
          {
            name: "Selfie",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/selfie.jpg",
          },
          {
            name: "PrDP",
            uploaded: true,
            verified: false,
            fileUrl: "/docs/prdp.pdf",
          },
          {
            name: "Proof of Address",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/address.pdf",
          },
        ],
        weeklyRate: 2700,
        initiationFee: 7500,
        paymentArrangement: false,
      };
    } else if (id === "2" || id.includes("rental")) {
      return {
        id: applicationId,
        applicantName: "Sarah M***",
        applicantEmail: "s***@e***.com",
        applicantPhone: "+27 83 *** ****",
        applicationDate: "2024-01-16",
        status: "pending",
        applicationType: "rental_application",
        vehicleName: "Toyota Corolla 2022",
        listingType: "rental",
        applicantAge: 32,
        applicantGender: "female",
        drivingExperienceYears: 8,
        documentsVerified: true,
        matchesPreferences: {
          age: true,
          gender: true,
          experience: true,
        },
        documents: [
          {
            name: "ID Document",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/id.pdf",
          },
          {
            name: "Driver's License",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/license.pdf",
          },
          {
            name: "Proof of Address",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/address.pdf",
          },
          {
            name: "Insurance Certificate",
            uploaded: true,
            verified: false,
            fileUrl: "/docs/insurance.pdf",
          },
        ],
      };
    } else if (id === "3" || id.includes("co_ownership")) {
      return {
        id: applicationId,
        applicantName: "Michael D***",
        applicantEmail: "m***@g***.com",
        applicantPhone: "+27 84 *** ****",
        applicationDate: "2024-01-14",
        status: "under_review",
        applicationType: "co_ownership_application",
        vehicleName: "Honda Civic 2023",
        listingType: "fractional",
        applicantAge: 29,
        applicantGender: "male",
        drivingExperienceYears: 6,
        documentsVerified: true,
        matchesPreferences: {
          age: true,
          gender: false,
          experience: true,
        },
        documents: [
          {
            name: "ID Document",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/id.pdf",
          },
          {
            name: "Driver's License",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/license.pdf",
          },
          {
            name: "Proof of Income",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/income.pdf",
          },
          {
            name: "Bank Statement",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/bank.pdf",
          },
        ],
      };
    } else {
      // Vehicle listing application
      return {
        id: applicationId,
        applicantName: "Lisa Johnson",
        applicantEmail: "<EMAIL>",
        applicantPhone: "+27 82 123 4567",
        applicationDate: "2024-01-13",
        status: "pending",
        applicationType: "vehicle_listing",
        make: "BMW",
        model: "X3",
        year: 2021,
        condition: "used",
        mileage: 45000,
        askingPrice: 450000,
        fractionOffer: 0.25,
        location: "Cape Town",
        description:
          "Well-maintained BMW X3 in excellent condition. Regular service history, no accidents.",
        documents: [
          {
            name: "Vehicle Registration",
            uploaded: true,
            verified: false,
            fileUrl: "/docs/registration.pdf",
          },
          {
            name: "Insurance Certificate",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/insurance.pdf",
          },
          {
            name: "Vehicle Photos",
            uploaded: true,
            verified: true,
            fileUrl: "/docs/photos.zip",
          },
          {
            name: "Service History",
            uploaded: true,
            verified: false,
            fileUrl: "/docs/service.pdf",
          },
        ],
      };
    }
  };

  const application = getApplicationData(applicationId);

  const handleDecision = (decision: "approved" | "rejected") => {
    setPendingDecision(decision);
    setIsDecisionDialogOpen(true);
  };

  const confirmDecision = () => {
    // Handle the decision logic here
    console.log(`Application ${pendingDecision}:`, {
      applicationId: application.id,
      decision: pendingDecision,
      reason: decisionReason,
    });

    setIsDecisionDialogOpen(false);
    setPendingDecision(null);
    setDecisionReason("");

    // Redirect or show success message
  };

  const handleDocumentVerification = (
    docName: string,
    status: boolean | "failed"
  ) => {
    setDocumentStatuses((prev) => ({
      ...prev,
      [docName]: status,
    }));

    // Here you would typically make an API call to update the document status
    console.log(`Document ${docName} verification:`, status);
  };

  const getDocumentVerificationStatus = (
    doc: ApplicationData["documents"][0]
  ) => {
    // Check if there's an override status, otherwise use the original status
    const overrideStatus = documentStatuses[doc.name];
    if (overrideStatus !== undefined) {
      return overrideStatus;
    }
    return doc.verified;
  };

  const getDocumentStatus = (doc: ApplicationData["documents"][0]) => {
    const verificationStatus = getDocumentVerificationStatus(doc);

    if (!doc.uploaded)
      return {
        color: "text-gray-400",
        bgColor: "bg-gray-100",
        icon: <XCircle size={16} />,
        text: "Not uploaded",
      };
    if (verificationStatus === "failed")
      return {
        color: "text-red-600",
        bgColor: "bg-red-100",
        icon: <XCircle size={16} />,
        text: "Verification failed",
      };
    if (verificationStatus === false)
      return {
        color: "text-yellow-600",
        bgColor: "bg-yellow-100",
        icon: <Clock size={16} />,
        text: "Pending verification",
      };
    if (verificationStatus === true)
      return {
        color: "text-green-600",
        bgColor: "bg-green-100",
        icon: <CheckCircle size={16} />,
        text: "Verified",
      };
    return {
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      icon: <Eye size={16} />,
      text: "Under review",
    };
  };

  const getStatusColor = (status: ApplicationData["status"]) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "under_review":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "approved":
        return "bg-green-100 text-green-800 border-green-200";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/applications">
              <ArrowLeft size={16} />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              {application.applicationType === "vehicle_listing"
                ? "Vehicle Listing Review"
                : "Application Review"}
            </h1>
            <p className="text-gray-600 mt-1">
              {application.applicantName} •{" "}
              {application.applicationType === "vehicle_listing"
                ? `${application.make} ${application.model} ${application.year}`
                : application.vehicleName}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge
            variant="outline"
            className={`${getStatusColor(application.status)} px-3 py-1`}
          >
            <span className="capitalize">
              {application.status.replace("_", " ")}
            </span>
          </Badge>
          {application.status === "pending" ||
          application.status === "under_review" ? (
            <div className="flex space-x-2">
              <Button
                variant="outline"
                className="text-red-600 border-red-200 hover:bg-red-50"
                onClick={() => handleDecision("rejected")}
              >
                <XCircle size={16} className="mr-2" />
                Reject
              </Button>
              <Button
                className="bg-[#009639] hover:bg-[#007A2F]"
                onClick={() => handleDecision("approved")}
              >
                <CheckCircle size={16} className="mr-2" />
                Approve
              </Button>
            </div>
          ) : null}
        </div>
      </div>

      {/* Application Status Alert */}
      {application.status === "pending" && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center">
              <AlertTriangle className="text-yellow-600 mr-3" size={20} />
              <div>
                <p className="font-medium text-yellow-800">
                  Application Pending Review
                </p>
                <p className="text-sm text-yellow-700">
                  This application is waiting for your review and decision.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger value="decision">Decision</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Applicant Profile */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User size={20} className="text-[#009639]" />
                    {application.applicationType === "vehicle_listing"
                      ? "Vehicle Owner Profile"
                      : "Applicant Profile"}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Full Name
                      </Label>
                      <p className="text-lg font-medium">
                        {application.applicantName}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        {application.applicationType === "vehicle_listing"
                          ? "Submission Date"
                          : "Application Date"}
                      </Label>
                      <p className="text-lg">
                        {formatDate(application.applicationDate)}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Email Address
                      </Label>
                      <div className="flex items-center">
                        <Mail size={16} className="text-gray-400 mr-2" />
                        <p>{application.applicantEmail}</p>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Phone Number
                      </Label>
                      <div className="flex items-center">
                        <Phone size={16} className="text-gray-400 mr-2" />
                        <p>{application.applicantPhone}</p>
                      </div>
                    </div>
                    {/* Additional fields for EARN side applications */}
                    {(application.applicationType === "rental_application" ||
                      application.applicationType ===
                        "co_ownership_application") && (
                      <>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Age
                          </Label>
                          <p className="text-lg">{application.applicantAge}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Gender
                          </Label>
                          <p className="text-lg capitalize">
                            {application.applicantGender}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Driving Experience
                          </Label>
                          <p className="text-lg">
                            {application.drivingExperienceYears} years
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Documents Status
                          </Label>
                          <p
                            className={`text-lg ${application.documentsVerified ? "text-green-600" : "text-yellow-600"}`}
                          >
                            {application.documentsVerified
                              ? "All Verified"
                              : "Pending Verification"}
                          </p>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Vehicle & Terms - Different content based on application type */}
              {application.applicationType === "vehicle_listing" ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Car size={20} className="text-[#009639]" />
                      Vehicle Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Vehicle
                        </Label>
                        <p className="text-lg font-medium">
                          {application.make} {application.model}{" "}
                          {application.year}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Condition
                        </Label>
                        <p className="text-lg capitalize">
                          {application.condition}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Mileage
                        </Label>
                        <p className="text-lg">
                          {application.mileage?.toLocaleString()} km
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Location
                        </Label>
                        <p className="text-lg">{application.location}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Asking Price
                        </Label>
                        <p className="text-lg font-bold text-[#009639]">
                          R{application.askingPrice?.toLocaleString()}
                        </p>
                      </div>
                      {application.fractionOffer && (
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Fraction Offer
                          </Label>
                          <p className="text-lg">
                            {(application.fractionOffer * 100).toFixed(0)}%
                            ownership
                          </p>
                        </div>
                      )}
                    </div>
                    {application.description && (
                      <div className="mt-4">
                        <Label className="text-sm font-medium text-gray-500">
                          Description
                        </Label>
                        <p className="text-sm text-gray-700 mt-1">
                          {application.description}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ) : application.applicationType === "vehicle_lease" ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Car size={20} className="text-[#009639]" />
                      Vehicle & Lease Terms
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Vehicle
                        </Label>
                        <p className="text-lg font-medium">
                          {application.vehicleName}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Weekly Rate
                        </Label>
                        <p className="text-lg font-bold text-[#009639]">
                          R{application.weeklyRate?.toLocaleString()}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Initiation Fee
                        </Label>
                        <p className="text-lg font-bold">
                          R{application.initiationFee?.toLocaleString()}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Payment Arrangement
                        </Label>
                        <p className="text-lg">
                          {application.paymentArrangement ? (
                            <span className="text-yellow-600">Requested</span>
                          ) : (
                            <span className="text-green-600">Not needed</span>
                          )}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Car size={20} className="text-[#009639]" />
                      Application Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Vehicle
                        </Label>
                        <p className="text-lg font-medium">
                          {application.vehicleName}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Application Type
                        </Label>
                        <p className="text-lg capitalize">
                          {application.listingType === "rental"
                            ? "Rental"
                            : "Co-ownership"}
                        </p>
                      </div>
                      {application.matchesPreferences && (
                        <>
                          <div>
                            <Label className="text-sm font-medium text-gray-500">
                              Age Match
                            </Label>
                            <p
                              className={`text-lg ${application.matchesPreferences.age ? "text-green-600" : "text-red-600"}`}
                            >
                              {application.matchesPreferences.age
                                ? "Matches"
                                : "Does not match"}
                            </p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-500">
                              Gender Match
                            </Label>
                            <p
                              className={`text-lg ${application.matchesPreferences.gender ? "text-green-600" : "text-red-600"}`}
                            >
                              {application.matchesPreferences.gender
                                ? "Matches"
                                : "Does not match"}
                            </p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-500">
                              Experience Match
                            </Label>
                            <p
                              className={`text-lg ${application.matchesPreferences.experience ? "text-green-600" : "text-red-600"}`}
                            >
                              {application.matchesPreferences.experience
                                ? "Matches"
                                : "Does not match"}
                            </p>
                          </div>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Experience - Only show for vehicle lease applications */}
              {application.applicationType === "vehicle_lease" &&
                application.experience && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Star size={20} className="text-[#009639]" />
                        E-hailing Experience
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {application.experience.hasExperience ? (
                        <div className="space-y-4">
                          <div className="flex items-center text-green-600 mb-4">
                            <CheckCircle size={20} className="mr-2" />
                            <span className="font-medium">
                              Has e-hailing experience
                            </span>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label className="text-sm font-medium text-gray-500">
                                Company
                              </Label>
                              <p className="text-lg">
                                {application.experience.company}
                              </p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-gray-500">
                                Duration
                              </Label>
                              <p className="text-lg">
                                {application.experience.duration?.replace(
                                  "-",
                                  " - "
                                )}
                              </p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-gray-500">
                                Work Type
                              </Label>
                              <p className="text-lg capitalize">
                                {application.experience.workType}
                              </p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-gray-500">
                                Profile Number
                              </Label>
                              <p className="text-lg font-mono">
                                {application.experience.profileNumber}
                              </p>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center text-yellow-600">
                          <AlertTriangle size={20} className="mr-2" />
                          <span className="font-medium">
                            No e-hailing experience
                          </span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
            </TabsContent>

            <TabsContent value="documents" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText size={20} className="text-[#009639]" />
                    Document Verification
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {application.documents.map((doc, index) => {
                      const status = getDocumentStatus(doc);
                      return (
                        <div
                          key={index}
                          className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow bg-white"
                        >
                          <div className="flex items-center justify-between mb-4">
                            <h4 className="text-lg font-semibold text-gray-900">
                              {doc.name}
                            </h4>
                            <div
                              className={`flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium ${status.color} ${status.bgColor}`}
                            >
                              {status.icon}
                              <span>{status.text}</span>
                            </div>
                          </div>
                          {doc.uploaded && (
                            <div className="space-y-3">
                              {/* Document Actions Row */}
                              <div className="flex flex-wrap gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center gap-2"
                                >
                                  <Eye size={16} />
                                  View Document
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center gap-2"
                                >
                                  <Download size={16} />
                                  Download
                                </Button>
                              </div>

                              {/* Verification Actions Row */}
                              <div className="border-t border-gray-100 pt-3">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm font-medium text-gray-700">
                                    Document Verification:
                                  </span>
                                  <div className="flex gap-2">
                                    {getDocumentVerificationStatus(doc) !==
                                      true && (
                                      <>
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="flex items-center gap-1 text-green-600 border-green-200 hover:bg-green-50"
                                          onClick={() =>
                                            handleDocumentVerification(
                                              doc.name,
                                              true
                                            )
                                          }
                                        >
                                          <CheckCircle size={14} />
                                          Verify
                                        </Button>
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="flex items-center gap-1 text-red-600 border-red-200 hover:bg-red-50"
                                          onClick={() =>
                                            handleDocumentVerification(
                                              doc.name,
                                              "failed"
                                            )
                                          }
                                        >
                                          <XCircle size={14} />
                                          Reject
                                        </Button>
                                      </>
                                    )}

                                    {/* Reset verification if already verified or failed */}
                                    {(getDocumentVerificationStatus(doc) ===
                                      true ||
                                      getDocumentVerificationStatus(doc) ===
                                        "failed") && (
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center gap-1 text-yellow-600 border-yellow-200 hover:bg-yellow-50"
                                        onClick={() =>
                                          handleDocumentVerification(
                                            doc.name,
                                            false
                                          )
                                        }
                                      >
                                        <Clock size={14} />
                                        Reset
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="decision" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle size={20} className="text-[#009639]" />
                    Application Decision
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label
                      htmlFor="decision-reason"
                      className="text-sm font-medium"
                    >
                      Decision Reason (Optional)
                    </Label>
                    <Textarea
                      id="decision-reason"
                      value={decisionReason}
                      onChange={(e) => setDecisionReason(e.target.value)}
                      placeholder="Add a note about your decision..."
                      className="mt-2"
                      rows={4}
                    />
                  </div>

                  <div className="flex space-x-4">
                    <Button
                      variant="outline"
                      className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                      onClick={() => handleDecision("rejected")}
                    >
                      <XCircle size={16} className="mr-2" />
                      Reject Application
                    </Button>
                    <Button
                      className="flex-1 bg-[#009639] hover:bg-[#007A2F]"
                      onClick={() => handleDecision("approved")}
                    >
                      <CheckCircle size={16} className="mr-2" />
                      Approve Application
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column - Quick Info & Actions */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Application Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Status</span>
                <Badge
                  variant="outline"
                  className={`${getStatusColor(application.status)}`}
                >
                  {application.status.replace("_", " ")}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Documents</span>
                <span className="text-sm font-medium">
                  {application.documents.filter((d) => d.uploaded).length}/
                  {application.documents.length} uploaded
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Verified</span>
                <span className="text-sm font-medium">
                  {
                    application.documents.filter((d) => d.verified === true)
                      .length
                  }
                  /{application.documents.filter((d) => d.uploaded).length}{" "}
                  verified
                </span>
              </div>
              {application.applicationType === "vehicle_lease" &&
                application.experience && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      E-hailing Experience
                    </span>
                    <span className="text-sm font-medium">
                      {application.experience.hasExperience ? "Yes" : "No"}
                    </span>
                  </div>
                )}
              {(application.applicationType === "rental_application" ||
                application.applicationType === "co_ownership_application") && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    Driving Experience
                  </span>
                  <span className="text-sm font-medium">
                    {application.drivingExperienceYears} years
                  </span>
                </div>
              )}
              {application.applicationType === "vehicle_listing" && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Vehicle Type</span>
                  <span className="text-sm font-medium">
                    {application.make} {application.model}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <Mail size={16} className="mr-2" />
                Contact Applicant
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Download size={16} className="mr-2" />
                Download All Documents
              </Button>
            </CardContent>
          </Card>

          {/* Application Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-[#009639] rounded-full mt-2 mr-3"></div>
                  <div>
                    <p className="text-sm font-medium">Application Submitted</p>
                    <p className="text-xs text-gray-500">
                      {formatShortDate(application.applicationDate)}
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-gray-300 rounded-full mt-2 mr-3"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      Documents Uploaded
                    </p>
                    <p className="text-xs text-gray-500">
                      {application.documents.filter((d) => d.uploaded).length}{" "}
                      of {application.documents.length} completed
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-gray-300 rounded-full mt-2 mr-3"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      Under Review
                    </p>
                    <p className="text-xs text-gray-500">
                      Pending admin decision
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Decision Confirmation Dialog */}
      <Dialog
        open={isDecisionDialogOpen}
        onOpenChange={setIsDecisionDialogOpen}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {pendingDecision === "approved" ? "Approve" : "Reject"}{" "}
              Application
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to {pendingDecision} this application from{" "}
              {application.applicantName}?
              {pendingDecision === "approved" && (
                <span className="block mt-2 text-sm">
                  This will notify the applicant and initiate the lease process.
                </span>
              )}
              {pendingDecision === "rejected" && (
                <span className="block mt-2 text-sm">
                  This will notify the applicant that their application was not
                  successful.
                </span>
              )}
            </DialogDescription>
          </DialogHeader>

          {decisionReason && (
            <div className="py-4">
              <Label className="text-sm font-medium">Decision Reason:</Label>
              <p className="text-sm text-gray-600 mt-1 p-3 bg-gray-50 rounded-md">
                {decisionReason}
              </p>
            </div>
          )}

          <DialogFooter className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => setIsDecisionDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              className={
                pendingDecision === "approved"
                  ? "bg-[#009639] hover:bg-[#007A2F]"
                  : "bg-red-600 hover:bg-red-700"
              }
              onClick={confirmDecision}
            >
              {pendingDecision === "approved" ? "Approve" : "Reject"}{" "}
              Application
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
